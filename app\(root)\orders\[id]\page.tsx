"use client";
import React, { useState, useEffect } from "react";
import { useOrders } from "@/contexts/OrderContext";
import { motion } from "framer-motion";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { formatCurrency } from "@/lib/utils";
import {
  ArrowLeft,
  Package,
  Truck,
  AlertTriangle,
  Loader2,
} from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import Image from "next/image";
import { toast } from "sonner";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import { Order } from "@/types/order";
import { IMAGE_PLACEHOLDER_URL } from "@/constants/helpers";

const OrderDetailsPage = () => {
  const { id } = useParams();
  const router = useRouter();
  const { getOrder, cancelOrder, updateOrderStatus } = useOrders();
  const [order, setOrder] = useState<Order | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch order data
  useEffect(() => {
    const fetchOrder = async () => {
      setIsLoading(true);
      try {
        const orderData = await getOrder(id as string);
        setOrder(orderData);
        if (!orderData) {
          setError("Order not found");
        }
      } catch (error) {
        console.error("Error fetching order:", error);
        setError("Failed to load order details");
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrder();
  }, [id, getOrder]);

  // Loading state
  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="container mx-auto px-4 py-16 text-center">
          <Loader2
            size={48}
            className="animate-spin mx-auto mb-4 text-accent"
          />
          <h2 className="text-xl font-semibold mb-2">
            Loading order details...
          </h2>
          <p className="text-gray-500">
            Please wait while we fetch your order information.
          </p>
        </div>
      </ProtectedRoute>
    );
  }

  // Error or order not found
  if (error || !order) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <AlertTriangle size={64} className="text-yellow-500 mx-auto mb-4" />
        <h1 className="text-2xl font-bold mb-4">Order Not Found</h1>
        <p className="mb-8">
          {error ||
            "The order you're looking for doesn't exist or has been removed."}
        </p>
        <Link href="/orders">
          <Button variant="default">View All Orders</Button>
        </Link>
      </div>
    );
  }

  // Function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  };

  // Function to get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "processing":
        return "bg-blue-100 text-blue-800";
      case "shipped":
        return "bg-purple-100 text-purple-800";
      case "delivered":
        return "bg-green-100 text-green-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Handle order cancellation
  const handleCancelOrder = async () => {
    if (window.confirm("Are you sure you want to cancel this order?")) {
      try {
        await cancelOrder(order.id);
        toast.success("Order cancelled successfully");
      } catch (error) {
        console.error("Error cancelling order:", error);
        toast.error("Failed to cancel order. Please try again.");
      }
    }
  };

  return (
    <ProtectedRoute>
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center mb-8">
          <Link href="/orders" className="mr-4">
            <Button variant="ghost" size="sm" className="gap-1">
              <ArrowLeft size={16} />
              Back to Orders
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Order Details</h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Order Details */}
          <div className="lg:col-span-2">
            <motion.div
              className="bg-white rounded-lg shadow-sm overflow-hidden mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="p-4 border-b border-gray-100 flex justify-between items-center">
                <div>
                  <h2 className="text-base sm:text-sm md:text-lg lg:text-xl font-semibold">
                    Order {order.id}
                  </h2>
                  <p className="text-sm text-gray-500">
                    Placed on {formatDate(order.createdAt)}
                  </p>
                </div>
                <div
                  className={`px-3 py-1 rounded-full text-base sm:text-[10px] md:text-lg lg:text-lg font-semibold ${getStatusColor(
                    order.status
                  )}`}
                >
                  {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                </div>
              </div>

              <div className="p-4">
                <h3 className="font-medium mb-3">Items</h3>
                <div className="divide-y divide-gray-100">
                  {order.items && order.items.length > 0 ? (
                    order.items.map((item, index) => {
                      // Skip rendering if item or item.product is undefined
                      if (!item || !item.product) {
                        console.error("Invalid item data:", item);
                        return (
                          <div
                            key={index}
                            className="py-3 flex items-center gap-4"
                          >
                            <div className="w-16 h-16 bg-gray-50 rounded-md overflow-hidden flex-shrink-0">
                              <Image
                                src={IMAGE_PLACEHOLDER_URL}
                                alt="Product"
                                width={64}
                                height={64}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            <div className="flex-grow">
                              <h4 className="font-medium">
                                Product information unavailable
                              </h4>
                              <p className="text-sm text-gray-500">
                                Item data missing
                              </p>
                            </div>
                          </div>
                        );
                      }

                      return (
                        <div
                          key={item.product.id || index}
                          className="py-3 flex items-center gap-4"
                        >
                          <div className="w-16 h-16 bg-gray-50 rounded-md overflow-hidden flex-shrink-0">
                            <Image
                              src={
                                item.product.images &&
                                item.product.images.length > 0
                                  ? item.product.images[0]
                                  : IMAGE_PLACEHOLDER_URL
                              }
                              alt={item.product.title || "Product"}
                              width={64}
                              height={64}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="flex-grow">
                            <Link href={`/products/${item.product.id}`}>
                              <h4 className="font-medium hover:text-accent transition-colors">
                                {item.product.title || "Product"}
                              </h4>
                            </Link>
                            <p className="text-sm text-gray-500">
                              {item.product.category || "Uncategorized"}
                            </p>
                          </div>
                          <div className="text-right">
                            <div className="font-medium">
                              {formatCurrency(
                                (item.product.price || 0) * (item.quantity || 1)
                              )}
                            </div>
                            <div className="text-sm text-gray-500">
                              {item.quantity || 1} ×{" "}
                              {formatCurrency(item.product.price || 0)}
                            </div>
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <div className="py-3 text-center text-gray-500">
                      No items found in this order
                    </div>
                  )}
                </div>
              </div>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Customer Information */}
              <motion.div
                className="bg-white rounded-lg shadow-sm p-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                <h3 className="font-medium mb-3">Customer Information</h3>
                <div className="space-y-2">
                  <p>
                    <span className="text-gray-500">Name:</span>{" "}
                    {order.customer && order.customer.firstName
                      ? `${order.customer.firstName} ${
                          order.customer.lastName || ""
                        }`
                      : "Not available"}
                  </p>
                  <p>
                    <span className="text-gray-500">Email:</span>{" "}
                    {order.customer && order.customer.email
                      ? order.customer.email
                      : "Not available"}
                  </p>
                  <p>
                    <span className="text-gray-500">Phone:</span>{" "}
                    {order.customer && order.customer.phone
                      ? order.customer.phone
                      : "Not available"}
                  </p>
                </div>
              </motion.div>

              {/* Shipping Information */}
              <motion.div
                className="bg-white rounded-lg shadow-sm p-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              >
                <h3 className="font-medium mb-3">Shipping Information</h3>
                <div className="space-y-2">
                  <p>
                    <span className="text-gray-500">Address:</span>{" "}
                    {order.shipping && order.shipping.address
                      ? order.shipping.address
                      : "Not available"}
                  </p>
                  <p>
                    <span className="text-gray-500">City:</span>{" "}
                    {order.shipping && order.shipping.city
                      ? order.shipping.city
                      : "Not available"}
                  </p>
                  {order.shipping && order.shipping.postalCode && (
                    <p>
                      <span className="text-gray-500">Postal Code:</span>{" "}
                      {order.shipping.postalCode}
                    </p>
                  )}
                </div>
              </motion.div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <motion.div
              className="bg-white rounded-lg shadow-sm p-4 sticky top-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
            >
              <h3 className="font-medium mb-4">Order Summary</h3>

              <div className="space-y-2 mb-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal</span>
                  <span>{formatCurrency(order.subtotal || 0)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Discount</span>
                  <span className="text-accent">
                    -{formatCurrency(order.discount || 0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Shipping</span>
                  <span>Free</span>
                </div>
                <div className="border-t border-gray-100 pt-2 mt-2">
                  <div className="flex justify-between font-semibold">
                    <span>Total</span>
                    <span>{formatCurrency(order.total || 0)}</span>
                  </div>
                </div>
              </div>

              {/* Order Status */}
              <div className="mb-6">
                <h4 className="text-sm font-medium mb-2">Order Status</h4>
                <div className="flex items-center gap-2">
                  {order.status === "pending" && (
                    <Package size={18} className="text-yellow-500" />
                  )}
                  {order.status === "processing" && (
                    <Package size={18} className="text-blue-500" />
                  )}
                  {order.status === "shipped" && (
                    <Truck size={18} className="text-purple-500" />
                  )}
                  {order.status === "delivered" && (
                    <Truck size={18} className="text-green-500" />
                  )}
                  {order.status === "cancelled" && (
                    <AlertTriangle size={18} className="text-red-500" />
                  )}
                  <span
                    className={`text-sm ${getStatusColor(
                      order.status
                    )} px-2 py-1 rounded-full`}
                  >
                    {order.status.charAt(0).toUpperCase() +
                      order.status.slice(1)}
                  </span>
                </div>
              </div>

              {/* Actions */}
              <div className="space-y-3">
                {order.status === "pending" && (
                  <Button
                    variant="outline"
                    className="w-full text-red-500 border-red-200 hover:bg-red-50 hover:text-red-600"
                    onClick={handleCancelOrder}
                  >
                    Cancel Order
                  </Button>
                )}

                <Link href="/orders">
                  <Button variant="ghost" className="w-full">
                    Back to Orders
                  </Button>
                </Link>

                <Link href="/products">
                  <Button variant="default" className="w-full">
                    Continue Shopping
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
};

export default OrderDetailsPage;
